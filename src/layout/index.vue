<template>
  <div class="layout-content">
    <div class="layout-asider">
      <BaseSide />
    </div>
    <div class="layout-main-wrapper">
      <div class="layout-header">
        <BaseHeader />
      </div>
      <div class="layout-main">
        <a-config-provider :theme="theme">
          <slot></slot>
        </a-config-provider>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import BaseSide from './BaseSide.vue'
import BaseHeader from './BaseHeader.vue'
import theme from '@/assets/styles/ant-theme.json'
</script>

<style lang="scss" scoped>
.layout-content {
  display: flex;
  height: 100vh;

  .layout-main-wrapper {
    flex: 1;

    .layout-header {
      height: 64px;
    }

    .layout-main {
      height: calc(100% - 64px);
    }
  }
}
</style>
