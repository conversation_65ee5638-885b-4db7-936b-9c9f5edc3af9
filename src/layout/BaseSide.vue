<template>
  <a-layout style="height: 100%">
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      width="180"
      class="sidebar"
    >
      <div class="logo" v-if="!collapsed"><Logo /></div>
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        @click="handleMenuClick"
      >
        <a-menu-item key="/">
          <span>首页</span>
        </a-menu-item>
        <a-sub-menu key="/produce">
          <template #title>生产管理</template>
          <a-menu-item key="/produce/work-order">
            <span>工单</span>
          </a-menu-item>
          <a-menu-item key="/produce/task">
            <span>任务</span>
          </a-menu-item>
          <a-menu-item key="/produce/material-list">
            <span>用料清单</span>
          </a-menu-item>
          <a-menu-item key="/produce/report">
            <span>报工</span>
          </a-menu-item>
          <a-menu-item key="/produce/report-workbench">
            <span>报工工作台</span>
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Logo from './Logo.vue'

const route = useRoute()
const router = useRouter()
const collapsed = ref<boolean>(false)
const selectedKeys = ref<string[]>([])
const openKeys = ref<string[]>([])

// 根据当前路由设置展开的菜单
const getOpenKeys = (path: string) => {
  const openMenus: string[] = []

  // 根据路径判断需要展开哪些父菜单
  if (path.startsWith('/produce')) {
    openMenus.push('/produce')
  }

  return openMenus
}

// 监听路由变化，确保能获取到正确的路径
watch(
  () => route.path,
  (newPath) => {
    if (newPath && newPath !== '/') {
      // 当刷新页面等情况时，能正确加载路由所匹配的菜单
      selectedKeys.value = [newPath]
      openKeys.value = getOpenKeys(newPath)
    }
  },
  { immediate: true }, // 立即执行一次
)

const handleMenuClick = ({ key }: { key: string }) => {
  router.push(key)
}
</script>

<style lang="scss" scoped>
:deep(.ant-layout-sider) {
  background: #111;
}
:deep(.ant-menu) {
  font-weight: bold;
}
:deep(.ant-menu.ant-menu-dark) {
  color: rgba(255, 255, 255, 0.65);
  background: #111;
}
:deep(.ant-menu-dark .ant-menu-inline.ant-menu-sub) {
  background-color: #222;
  border-radius: $border-radius-md;
  width: auto;
  margin-left: $spacing-sm;
  margin-right: $spacing-sm;
}
:deep(.ant-menu-item.ant-menu-item-selected) {
  color: #fff;
  background-color: #111;
}
:deep(.ant-menu-item:not(.ant-menu-item-selected):active) {
  background-color: #111 !important;
}
:deep(.ant-menu-item:not(.ant-menu-item-selected):hover) {
  background-color: #111 !important;
}
:deep(.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-submenu-title:active) {
  background-color: #111 !important;
}
:deep(.ant-menu.ant-menu-dark .ant-menu-sub .ant-menu-item-selected .ant-menu-title-content span) {
  color: $primary-color;
  font-weight: 700;
}
</style>
