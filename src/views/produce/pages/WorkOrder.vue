<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar
        :tabs="tabsData"
        @tab-click="handleTabClick"
        @more-click="handleMoreClick"
        @add-click="handleAddClick"
      />
      <div class="search-row">
        <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        <AdvancedSearchInput
          v-model="searchQuery"
          :search-options="searchOptions"
          @search="handleSearch"
          @type-change="handleSearchTypeChange"
        />
      </div>
    </div>
    <div class="list-tabel-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ProfileFilled } from '@ant-design/icons-vue'
import QuickSearchTabbar from '@/components/QuickSearchTabbar.vue'
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'
import AdvancedSearchInput from '@/components/AdvancedSearchInput.vue'

// 定义标签数据
const tabsData = ref([
  {
    name: '全部',
    icon: ProfileFilled,
    active: true,
  },
  {
    name: '看板1',
    icon: ProfileFilled,
    active: false,
  },
  {
    name: '看板2',
    icon: ProfileFilled,
    active: false,
  },
])

// 定义分组数据
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])

// 搜索相关数据
const searchQuery = ref('')
const searchOptions = ref([
  {
    label: '工单编号、产品编号、产品名称、产品规格',
    value: 'all',
    placeholder: '输入产品编号搜索',
  },
  { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
  { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
  { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
  { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
  { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
  { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
  { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
])

// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  // 更新选中状态
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

const handleMoreClick = (tab: any, index: number) => {
  console.log('More clicked:', tab, index)
}

const handleAddClick = () => {
  console.log('Add clicked')
}

const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}

const handleSearch = (data: { type: string; value: string }) => {
  console.log('Search triggered:', data)
  // 这里可以调用API进行搜索
}

const handleSearchTypeChange = (type: string) => {
  console.log('Search type changed:', type)
}
</script>

<style lang="scss" scoped>
.list-action-wrapper {
  height: 47px;
  padding: 0 $spacing-lg;
  border-bottom: 1px solid $border-color;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: $spacing-base;
}
</style>
