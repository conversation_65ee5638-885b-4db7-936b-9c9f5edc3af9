<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar
        :tabs="tabsData"
        @tab-click="handleTabClick"
        @more-click="handleMoreClick"
        @add-click="handleAddClick"
      />
      <div class="search-row">
        <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        <div class="advancedSearchInput"></div>
      </div>
    </div>
    <div class="list-tabel-wrapper"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ProfileFilled } from '@ant-design/icons-vue'
import QuickSearchTabbar from '@/components/QuickSearchTabbar.vue'
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'

// 定义标签数据
const tabsData = ref([
  {
    name: '全部',
    icon: ProfileFilled,
    active: true,
  },
  {
    name: '看板1',
    icon: ProfileFilled,
    active: false,
  },
  {
    name: '看板2',
    icon: ProfileFilled,
    active: false,
  },
])

// 定义分组数据
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])

// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  // 更新选中状态
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

const handleMoreClick = (tab: any, index: number) => {
  console.log('More clicked:', tab, index)
}

const handleAddClick = () => {
  console.log('Add clicked')
}

const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}
</script>

<style lang="scss" scoped>
.list-action-wrapper {
  height: 47px;
  padding: 0 $spacing-lg;
  border-bottom: 1px solid $border-color;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-top: $spacing-base;
}
</style>
