<template>
  <div class="advanced-search-input">
    <!-- 搜索类型下拉选择器 -->
    <div class="search-type-selector">
      <a-dropdown v-model:open="dropdownVisible" :trigger="['click']" placement="bottomLeft">
        <a-button
          class="type-selector-btn"
          :icon="h(DownOutlined)"
          @click="dropdownVisible = !dropdownVisible"
        />
        <template #overlay>
          <a-menu @click="handleTypeSelect" :selectedKeys="[selectedType]">
            <a-menu-item v-for="option in searchOptions" :key="option.value">
              {{ option.label }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>

    <!-- 搜索输入框 -->
    <div class="search-input-wrapper">
      <a-input
        v-model:value="searchValue"
        :placeholder="currentPlaceholder"
        class="search-input"
        @pressEnter="handleSearch"
        @input="handleInput"
      />
      <a-input-search
      v-model:value="value"
      placeholder="input search text"
      enter-button
      @search="onSearch"
    />
    </div>

    <!-- 搜索按钮 -->
    <div class="search-button-wrapper">
      <a-button type="primary" class="search-btn" :icon="h(SearchOutlined)" @click="handleSearch" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { DownOutlined, SearchOutlined } from '@ant-design/icons-vue'

// 定义搜索选项接口
interface SearchOption {
  label: string
  value: string
  placeholder?: string
}

// 定义组件属性
interface Props {
  searchOptions?: SearchOption[]
  defaultType?: string
  placeholder?: string
  modelValue?: string
}

// 定义事件
interface Emits {
  (e: 'search', data: { type: string; value: string }): void
  (e: 'update:modelValue', value: string): void
  (e: 'typeChange', type: string): void
}

const props = withDefaults(defineProps<Props>(), {
  searchOptions: () => [
    {
      label: '工单编号、产品编号、产品名称、产品规格',
      value: 'all',
      placeholder: '输入产品编号搜索',
    },
    { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
    { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
    { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
    { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
    { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
    { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
    { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
  ],
  defaultType: 'all',
  placeholder: '输入产品编号搜索',
})

const emit = defineEmits<Emits>()

// 响应式数据
const dropdownVisible = ref(false)
const selectedType = ref(props.defaultType)
const searchValue = ref(props.modelValue || '')

// 计算属性
const currentPlaceholder = computed(() => {
  const option = props.searchOptions.find((opt) => opt.value === selectedType.value)
  return option?.placeholder || props.placeholder
})

// 事件处理函数
const handleTypeSelect = ({ key }: { key: string }) => {
  selectedType.value = key
  dropdownVisible.value = false
  emit('typeChange', key)
}

const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  searchValue.value = target.value
  emit('update:modelValue', target.value)
}

const handleSearch = () => {
  emit('search', {
    type: selectedType.value,
    value: searchValue.value.trim(),
  })
}

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== searchValue.value) {
      searchValue.value = newValue || ''
    }
  },
)
</script>

<style lang="scss" scoped>
.advanced-search-input {
  display: flex;
  align-items: center;
  border: 1px solid $border-color;
  border-radius: $border-radius-md;
  background-color: $white;
  overflow: hidden;
  min-width: 400px;
  height: 40px;

  .search-input-wrapper {
    flex: 1;

    .search-input {
      border: none;
      border-radius: 0;
      box-shadow: none;
      height: 38px;

      &:focus {
        box-shadow: none;
        border-color: transparent;
      }

      &::placeholder {
        color: $gray-400;
        font-size: $font-size-sm;
      }
    }
  }

  .search-button-wrapper {
    .search-btn {
      border: none;
      border-radius: 0;
      height: 38px;
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $primary-color;

      &:hover {
        background-color: $primary-dark;
      }
    }
  }

  &:focus-within {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba(2, 185, 128, 0.1);
  }
}

// 下拉菜单样式
:deep(.ant-dropdown-menu) {
  max-height: 300px;
  overflow-y: auto;
  border-radius: $border-radius-md;
  box-shadow: $shadow-lg;
}

:deep(.ant-dropdown-menu-item) {
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-sm;

  &:hover {
    background-color: $gray-50;
  }

  &.ant-dropdown-menu-item-selected {
    background-color: $primary-light;
    color: $primary-darker;
  }
}
</style>
